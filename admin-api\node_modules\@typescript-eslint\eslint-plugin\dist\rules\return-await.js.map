{"version": 3, "file": "return-await.js", "sourceRoot": "", "sources": ["../../src/rules/return-await.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAQiB;AACjB,yEAAsE;AAkBtE,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,cAAc;IACpB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,kDAAkD;YAC/D,oBAAoB,EAAE,IAAI;YAC1B,eAAe,EAAE,iBAAiB;SACnC;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,IAAI,EAAE,SAAS;QACf,QAAQ,EAAE;YACR,eAAe,EACb,kEAAkE;YACpE,sBAAsB,EACpB,8DAA8D;YAChE,oBAAoB,EAClB,2DAA2D;YAC7D,8BAA8B,EAC5B,iFAAiF;YACnF,gCAAgC,EAC9B,oFAAoF;SACvF;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,IAAI,EAAE;oBACJ,cAAc;oBACd,QAAQ;oBACR,OAAO;oBACP,iCAAiC;iBACf;aACrB;SACF;KACF;IACD,cAAc,EAAE,CAAC,cAAc,CAAC;IAEhC,MAAM,CAAC,OAAO,EAAE,CAAC,MAAM,CAAC;QACtB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,MAAM,cAAc,GAAgB,EAAE,CAAC;QAEvC,SAAS,aAAa,CAAC,IAAkB;YACvC,cAAc,CAAC,IAAI,CAAC;gBAClB,QAAQ,EAAE,IAAI,CAAC,KAAK;gBACpB,UAAU,EAAE,IAAI;aACjB,CAAC,CAAC;QACL,CAAC;QAED,SAAS,YAAY;YACnB,cAAc,CAAC,GAAG,EAAE,CAAC;QACvB,CAAC;QAED,SAAS,iCAAiC,CAAC,IAAmB;YAC5D,qEAAqE;YACrE,IAAI,KAAK,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,aAAa,GAAG,KAAK,CAAC,aAAa,CAAC;YAC1C,OAAO,IAAI,EAAE,CAAC;gBACZ,KAAK,MAAM,QAAQ,IAAI,KAAK,CAAC,SAAS,EAAE,CAAC;oBACvC,IAAI,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;wBAC/B,iEAAiE;wBACjE,gEAAgE;wBAChE,mCAAmC;wBACnC,SAAS;oBACX,CAAC;oBAED,MAAM,WAAW,GAAG,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;oBACrC,MAAM,cAAc,GAAG,WAAW,CAAC,IAAI,CAAC;oBACxC,MAAM,eAAe,GACnB,cAAc,CAAC,MAAsC,CAAC;oBAExD,qEAAqE;oBACrE,8DAA8D;oBAC9D,IACE,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC;wBACvD,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,EACvC,CAAC;wBACD,OAAO,IAAI,CAAC;oBACd,CAAC;gBACH,CAAC;gBAED,IAAI,KAAK,KAAK,aAAa,EAAE,CAAC;oBAC5B,wCAAwC;oBACxC,MAAM;gBACR,CAAC;gBAED,mEAAmE;gBACnE,uEAAuE;gBACvE,wBAAwB;gBACxB,KAAK,GAAG,IAAA,iBAAU,EAChB,KAAK,CAAC,KAAK,EACX,wGAAwG,CACzG,CAAC;YACJ,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED;;;;WAIG;QACH,SAAS,4BAA4B,CAAC,IAAa;YACjD,0EAA0E;YAC1E,yEAAyE;YACzE,0EAA0E;YAC1E,oEAAoE;YACpE,wEAAwE;YACxE,QAAQ;YACR,MAAM,iBAAiB,GAAG,0BAA0B,CAAC,IAAI,CAAC,CAAC;YAC3D,IAAI,iBAAiB,IAAI,IAAI,EAAE,CAAC;gBAC9B,OAAO,KAAK,CAAC;YACf,CAAC;YAED,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,iBAAiB,CAAC;YAElD,QAAQ,KAAK,EAAE,CAAC;gBACd,KAAK,KAAK;oBACR,+DAA+D;oBAC/D,wDAAwD;oBACxD,OAAO,IAAI,CAAC;gBACd,KAAK,OAAO;oBACV,uEAAuE;oBACvE,gBAAgB;oBAChB,IAAI,YAAY,CAAC,YAAY,IAAI,IAAI,EAAE,CAAC;wBACtC,OAAO,IAAI,CAAC;oBACd,CAAC;oBAED,qBAAqB;oBACrB,OAAO,4BAA4B,CAAC,YAAY,CAAC,CAAC;gBACpD,KAAK,SAAS;oBACZ,OAAO,4BAA4B,CAAC,YAAY,CAAC,CAAC;gBACpD,OAAO,CAAC,CAAC,CAAC;oBACR,MAAM,OAAO,GAAU,KAAK,CAAC;oBAC7B,MAAM,IAAI,KAAK,CAAC,0BAA0B,MAAM,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBAC/D,CAAC;YACH,CAAC;QACH,CAAC;QAOD;;;;;WAKG;QACH,SAAS,0BAA0B,CACjC,IAAa;YAEb,IAAI,KAAK,GAAG,IAAI,CAAC;YACjB,IAAI,QAAQ,GAAG,IAAI,CAAC,MAA6B,CAAC;YAElD,OAAO,QAAQ,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAChD,IAAI,EAAE,CAAC,cAAc,CAAC,QAAQ,CAAC,EAAE,CAAC;oBAChC,IAAI,KAA8C,CAAC;oBACnD,IAAI,KAAK,KAAK,QAAQ,CAAC,QAAQ,EAAE,CAAC;wBAChC,KAAK,GAAG,KAAK,CAAC;oBAChB,CAAC;yBAAM,IAAI,KAAK,KAAK,QAAQ,CAAC,WAAW,EAAE,CAAC;wBAC1C,KAAK,GAAG,OAAO,CAAC;oBAClB,CAAC;yBAAM,IAAI,KAAK,KAAK,QAAQ,CAAC,YAAY,EAAE,CAAC;wBAC3C,KAAK,GAAG,SAAS,CAAC;oBACpB,CAAC;oBAED,OAAO;wBACL,YAAY,EAAE,QAAQ;wBACtB,KAAK,EAAE,IAAA,iBAAU,EACf,KAAK,EACL,8EAA8E,CAC/E;qBACF,CAAC;gBACJ,CAAC;gBACD,KAAK,GAAG,QAAQ,CAAC;gBACjB,QAAQ,GAAG,QAAQ,CAAC,MAAM,CAAC;YAC7B,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,SAAS,WAAW,CAClB,KAAyB,EACzB,IAAyB;YAEzB,qDAAqD;YACrD,wBAAwB,CAAC,IAAI,CAAC,IAAA,wBAAiB,EAAC,IAAI,CAAC,EAAE,CAAC;gBACtD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,IAAI,EAAE,qBAAc,CAAC,CAAC;YAC1E,gDAAgD;YAChD,wBAAwB,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;gBACzC,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,OAAO,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YACpC,IAAI,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAChC,wEAAwE;YACxE,MAAM,SAAS,GAAG,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,UAAU,EAAE;gBAC7D,eAAe,EAAE,IAAI;aACtB,CAAC,CAAC;YACH,IAAI,SAAS,EAAE,CAAC;gBACd,KAAK,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7B,CAAC;YAED,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,OAAO,EAAE,KAAK,CAAC,CAAC,CAAC;QAC7C,CAAC;QAED,SAAS,WAAW,CAClB,KAAyB,EACzB,IAAyB,EACzB,iBAA0B;YAE1B,IAAI,iBAAiB,EAAE,CAAC;gBACtB,OAAO,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAChD,CAAC;YACD,OAAO;gBACL,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,SAAS,CAAC;gBACvC,KAAK,CAAC,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC;aACjC,CAAC;QACJ,CAAC;QAED,SAAS,2BAA2B,CAAC,IAAa;YAChD,MAAM,QAAQ,GAAG,EAAE,CAAC,kBAAkB,CAAC,IAAI,CAAC;gBAC1C,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI;gBACzB,CAAC,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC;YAC1B,MAAM,cAAc,GAAG,IAAA,6CAAqB,EAAC,IAAI,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YAClE,MAAM,eAAe,GAAG,IAAA,6CAAqB,EAC3C,EAAE,CAAC,UAAU,CAAC,eAAe,EAC7B,EAAE,CAAC,UAAU,CAAC,OAAO,CACtB,CAAC;YACF,OAAO,cAAc,GAAG,eAAe,CAAC;QAC1C,CAAC;QAED,SAAS,IAAI,CAAC,IAAyB,EAAE,UAAmB;YAC1D,IAAI,KAAc,CAAC;YAEnB,MAAM,OAAO,GAAG,EAAE,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC;YAEjD,IAAI,OAAO,EAAE,CAAC;gBACZ,KAAK,GAAG,UAAU,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;YACnC,CAAC;iBAAM,CAAC;gBACN,KAAK,GAAG,UAAU,CAAC;YACrB,CAAC;YAED,MAAM,IAAI,GAAG,OAAO,CAAC,iBAAiB,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,UAAU,GAAG,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,UAAU,EAAE,IAAI,CAAC,CAAC;YAErE,gCAAgC;YAEhC,IAAI,CAAC,UAAU,EAAE,CAAC;gBAChB,IAAI,OAAO,EAAE,CAAC;oBACZ,iDAAiD;oBACjD,MAAM,UAAU,GAAG,CAAC,CAAC,IAAA,oBAAa,EAAC,IAAI,CAAC,IAAI,IAAA,wBAAiB,EAAC,IAAI,CAAC,CAAC,CAAC;oBAErE,OAAO,CAAC,MAAM,CAAC;wBACb,SAAS,EAAE,iBAAiB;wBAC5B,IAAI;wBACJ,GAAG,YAAY,CAAC,UAAU,EAAE;4BAC1B,SAAS,EAAE,iBAAiB;4BAC5B,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;yBACvC,CAAC;qBACH,CAAC,CAAC;gBACL,CAAC;gBACD,OAAO;YACT,CAAC;YAED,4CAA4C;YAE5C,MAAM,oBAAoB,GACxB,4BAA4B,CAAC,UAAU,CAAC;gBACxC,iCAAiC,CAAC,IAAI,CAAC,CAAC;YAC1C,MAAM,UAAU,GAAG,CAAC,oBAAoB,CAAC;YAEzC,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,MAAgB,CAAC,CAAC;YAE7D,MAAM,2BAA2B,GAAG,oBAAoB;gBACtD,CAAC,CAAC,iBAAiB,CAAC,oBAAoB;gBACxC,CAAC,CAAC,iBAAiB,CAAC,eAAe,CAAC;YAEtC,QAAQ,2BAA2B,EAAE,CAAC;gBACpC,KAAK,YAAY;oBACf,MAAM;gBACR,KAAK,OAAO;oBACV,IAAI,CAAC,OAAO,EAAE,CAAC;wBACb,OAAO,CAAC,MAAM,CAAC;4BACb,SAAS,EAAE,sBAAsB;4BACjC,IAAI;4BACJ,GAAG,YAAY,CAAC,UAAU,EAAE;gCAC1B,SAAS,EAAE,gCAAgC;gCAC3C,GAAG,EAAE,KAAK,CAAC,EAAE,CACX,WAAW,CACT,KAAK,EACL,IAAI,EACJ,2BAA2B,CAAC,UAAU,CAAC,CACxC;6BACJ,CAAC;yBACH,CAAC,CAAC;oBACL,CAAC;oBACD,MAAM;gBACR,KAAK,UAAU;oBACb,IAAI,OAAO,EAAE,CAAC;wBACZ,OAAO,CAAC,MAAM,CAAC;4BACb,SAAS,EAAE,wBAAwB;4BACnC,IAAI;4BACJ,GAAG,YAAY,CAAC,UAAU,EAAE;gCAC1B,SAAS,EAAE,kCAAkC;gCAC7C,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,EAAE,IAAI,CAAC;6BACvC,CAAC;yBACH,CAAC,CAAC;oBACL,CAAC;oBACD,MAAM;YACV,CAAC;QACH,CAAC;QAED,SAAS,yBAAyB,CAChC,IAAyB;YAEzB,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,qBAAqB,EAAE,CAAC;gBACvD,OAAO;oBACL,GAAG,yBAAyB,CAAC,IAAI,CAAC,SAAS,CAAC;oBAC5C,GAAG,yBAAyB,CAAC,IAAI,CAAC,UAAU,CAAC;iBAC9C,CAAC;YACJ,CAAC;YACD,OAAO,CAAC,IAAI,CAAC,CAAC;QAChB,CAAC;QAED,OAAO;YACL,mBAAmB,EAAE,aAAa;YAClC,kBAAkB,EAAE,aAAa;YACjC,uBAAuB,EAAE,aAAa;YAEtC,0BAA0B,EAAE,YAAY;YACxC,yBAAyB,EAAE,YAAY;YACvC,8BAA8B,EAAE,YAAY;YAE5C,kEAAkE;YAClE,4CAA4C,CAC1C,IAAsC;gBAEtC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc,EAAE,CAAC;oBACrD,yBAAyB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;wBAClD,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;wBACxD,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oBACrB,CAAC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,MAAM,SAAS,GAAG,cAAc,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;gBACxC,IAAI,CAAC,SAAS,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;oBAC3C,OAAO;gBACT,CAAC;gBACD,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;oBACtD,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;oBACxD,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACrB,CAAC,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC;AASH,SAAS,gBAAgB,CAAC,MAAc;IACtC,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,QAAQ;YACX,OAAO;gBACL,eAAe,EAAE,OAAO;gBACxB,oBAAoB,EAAE,OAAO;aAC9B,CAAC;QACJ,KAAK,OAAO;YACV,OAAO;gBACL,eAAe,EAAE,UAAU;gBAC3B,oBAAoB,EAAE,UAAU;aACjC,CAAC;QACJ,KAAK,iCAAiC;YACpC,OAAO;gBACL,eAAe,EAAE,YAAY;gBAC7B,oBAAoB,EAAE,OAAO;aAC9B,CAAC;QACJ,KAAK,cAAc;YACjB,OAAO;gBACL,eAAe,EAAE,UAAU;gBAC3B,oBAAoB,EAAE,OAAO;aAC9B,CAAC;IACN,CAAC;AACH,CAAC;AAED,SAAS,YAAY,CACnB,MAAe,EACf,UAA0D;IAI1D,OAAO,MAAM,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,CAAC,UAAU,CAAC,EAAE,CAAC;AACtE,CAAC"}