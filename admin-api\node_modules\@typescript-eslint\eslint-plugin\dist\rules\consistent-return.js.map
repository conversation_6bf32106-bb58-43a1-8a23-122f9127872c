{"version": 3, "file": "consistent-return.js", "sourceRoot": "", "sources": ["../../src/rules/consistent-return.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,sDAAwC;AACxC,+CAAiC;AAMjC,kCAAuE;AACvE,iEAA8D;AAE9D,MAAM,QAAQ,GAAG,IAAA,qCAAiB,EAAC,mBAAmB,CAAC,CAAC;AAUxD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,sEAAsE;YACxE,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,cAAc,EAAE,QAAQ,CAAC,IAAI,CAAC,cAAc;QAC5C,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,MAAM;QAC5B,QAAQ,EAAE,QAAQ,CAAC,IAAI,CAAC,QAAQ;KACjC;IACD,cAAc,EAAE,CAAC,EAAE,2BAA2B,EAAE,KAAK,EAAE,CAAC;IACxD,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAClD,MAAM,KAAK,GAAG,QAAQ,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;QACvC,MAAM,SAAS,GAAmB,EAAE,CAAC;QACrC,MAAM,2BAA2B,GAC/B,OAAO,EAAE,2BAA2B,KAAK,IAAI,CAAC;QAEhD,SAAS,aAAa,CAAC,IAAkB;YACvC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;QACvB,CAAC;QAED,SAAS,YAAY;YACnB,SAAS,CAAC,GAAG,EAAE,CAAC;QAClB,CAAC;QAED,SAAS,kBAAkB;YACzB,OAAO,SAAS,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,IAAI,CAAC;QACjD,CAAC;QAED,SAAS,aAAa,CAAC,IAAa,EAAE,IAAa;YACjD,IACE,OAAO,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,CAAC;gBAC3C,OAAO,CAAC,eAAe,CAAC,IAAI,CAAC,EAC7B,CAAC;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC5C,IAAI,WAAW,EAAE,CAAC;oBAChB,IAAI,IAAA,oBAAa,EAAC,WAAW,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC;wBAClD,OAAO,IAAI,CAAC;oBACd,CAAC;oBACD,OAAO,aAAa,CAAC,IAAI,EAAE,WAAW,CAAC,CAAC;gBAC1C,CAAC;YACH,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,0BAA0B,CAAC,IAAkB;YACpD,MAAM,YAAY,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YACtD,MAAM,MAAM,GAAG,QAAQ,CAAC,qBAAqB,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YACxD,MAAM,cAAc,GAAG,YAAY,CAAC,iBAAiB,EAAE,CAAC;YAExD,OAAO,cAAc,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBACrC,MAAM,UAAU,GAAG,SAAS,CAAC,aAAa,EAAE,CAAC;gBAC7C,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACf,OAAO,aAAa,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC;gBAC3C,CAAC;gBACD,OAAO,IAAA,oBAAa,EAAC,UAAU,EAAE,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC;YACtD,CAAC,CAAC,CAAC;QACL,CAAC;QAED,OAAO;YACL,GAAG,KAAK;YACR,mBAAmB,EAAE,aAAa;YAClC,0BAA0B,CAAC,IAAI;gBAC7B,YAAY,EAAE,CAAC;gBACf,KAAK,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,CAAC;YAC1C,CAAC;YACD,kBAAkB,EAAE,aAAa;YACjC,yBAAyB,CAAC,IAAI;gBAC5B,YAAY,EAAE,CAAC;gBACf,KAAK,CAAC,yBAAyB,CAAC,CAAC,IAAI,CAAC,CAAC;YACzC,CAAC;YACD,uBAAuB,EAAE,aAAa;YACtC,8BAA8B,CAAC,IAAI;gBACjC,YAAY,EAAE,CAAC;gBACf,KAAK,CAAC,8BAA8B,CAAC,CAAC,IAAI,CAAC,CAAC;YAC9C,CAAC;YACD,eAAe,CAAC,IAAI;gBAClB,MAAM,YAAY,GAAG,kBAAkB,EAAE,CAAC;gBAC1C,IACE,CAAC,IAAI,CAAC,QAAQ;oBACd,YAAY;oBACZ,0BAA0B,CAAC,YAAY,CAAC,EACxC,CAAC;oBACD,OAAO;gBACT,CAAC;gBACD,IAAI,2BAA2B,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;oBACjD,MAAM,eAAe,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;oBAClE,IAAI,eAAe,CAAC,KAAK,KAAK,EAAE,CAAC,SAAS,CAAC,SAAS,EAAE,CAAC;wBACrD,KAAK,CAAC,eAAe,CAAC;4BACpB,GAAG,IAAI;4BACP,QAAQ,EAAE,IAAI;yBACf,CAAC,CAAC;wBACH,OAAO;oBACT,CAAC;gBACH,CAAC;gBAED,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAC;YAC9B,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}