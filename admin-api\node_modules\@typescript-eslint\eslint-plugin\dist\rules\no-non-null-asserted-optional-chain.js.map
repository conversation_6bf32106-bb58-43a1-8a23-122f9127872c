{"version": 3, "file": "no-non-null-asserted-optional-chain.js", "sourceRoot": "", "sources": ["../../src/rules/no-non-null-asserted-optional-chain.ts"], "names": [], "mappings": ";;AAEA,kCAAqC;AAErC,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,qCAAqC;IAC3C,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,iEAAiE;YACnE,WAAW,EAAE,aAAa;SAC3B;QACD,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,sBAAsB,EACpB,6GAA6G;YAC/G,sBAAsB,EAAE,2CAA2C;SACpE;QACD,MAAM,EAAE,EAAE;KACX;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,OAAO;YACL,2EAA2E;YAC3E,UAAU;YACV,WAAW;YACX,uCAAuC,CACrC,IAA8B;gBAE9B,qCAAqC;gBACrC,MAAM,MAAM,GAAG,IAAI,CAAC,MAAsC,CAAC;gBAC3D,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,wBAAwB;oBACnC,oFAAoF;oBACpF,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,wBAAwB;4BACnC,GAAG,CAAC,KAAK;gCACP,OAAO,KAAK,CAAC,WAAW,CAAC;oCACvB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;oCACnB,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC;iCAChB,CAAC,CAAC;4BACL,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;YAED,iFAAiF;YACjF,QAAQ;YACR,SAAS;YACT,uCAAuC,CACrC,IAAkC;gBAElC,OAAO,CAAC,MAAM,CAAC;oBACb,IAAI;oBACJ,SAAS,EAAE,wBAAwB;oBACnC,oFAAoF;oBACpF,OAAO,EAAE;wBACP;4BACE,SAAS,EAAE,wBAAwB;4BACnC,GAAG,CAAC,KAAK;gCACP,OAAO,KAAK,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;4BAC/D,CAAC;yBACF;qBACF;iBACF,CAAC,CAAC;YACL,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}