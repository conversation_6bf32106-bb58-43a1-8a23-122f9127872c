{"version": 3, "file": "no-implied-eval.js", "sourceRoot": "", "sources": ["../../src/rules/no-implied-eval.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACA,oDAA0D;AAC1D,sDAAwC;AACxC,+CAAiC;AAEjC,kCAA6E;AAE7E,MAAM,oBAAoB,GAAG,UAAU,CAAC;AACxC,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC,CAAC;AACtE,MAAM,iBAAiB,GAAG,IAAI,GAAG,CAAC;IAChC,cAAc;IACd,aAAa;IACb,YAAY;IACZ,YAAY;CACb,CAAC,CAAC;AAEH,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,iBAAiB;IACvB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EAAE,2CAA2C;YACxD,WAAW,EAAE,aAAa;YAC1B,eAAe,EAAE,IAAI;YACrB,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,kBAAkB,EAAE,4CAA4C;YAChE,qBAAqB,EACnB,wEAAwE;SAC3E;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD,SAAS,aAAa,CAAC,IAAyB;YAC9C,IAAI,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;gBAC5C,OAAO,IAAI,CAAC,IAAI,CAAC;YACnB,CAAC;YAED,IACE,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC9C,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EACvC,CAAC;gBACD,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,EAAE,CAAC;oBACrD,OAAO,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;gBAC5B,CAAC;gBAED,IACE,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBAC7C,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ,EACvC,CAAC;oBACD,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAC7B,CAAC;YACH,CAAC;YAED,OAAO,IAAI,CAAC;QACd,CAAC;QAED,SAAS,cAAc,CAAC,IAAmB;YACzC,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAC9C,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;YAEhC,IACE,MAAM;gBACN,OAAO,CAAC,eAAe,CACrB,MAAM,EACN,EAAE,CAAC,WAAW,CAAC,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAChD,EACD,CAAC;gBACD,OAAO,IAAI,CAAC;YACd,CAAC;YAED,IAAI,IAAA,0BAAmB,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,oBAAoB,CAAC,EAAE,CAAC;gBACtE,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,UAAU,GAAG,OAAO,CAAC,mBAAmB,CAC5C,IAAI,EACJ,EAAE,CAAC,aAAa,CAAC,IAAI,CACtB,CAAC;YAEF,OAAO,UAAU,CAAC,MAAM,GAAG,CAAC,CAAC;QAC/B,CAAC;QAED,SAAS,MAAM,CAAC,IAAmB;YACjC,OAAO,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;gBAClD,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACvB,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC;QACtE,CAAC;QAED,SAAS,UAAU,CAAC,IAAmB;YACrC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;gBAClB,KAAK,sBAAc,CAAC,uBAAuB,CAAC;gBAC5C,KAAK,sBAAc,CAAC,mBAAmB,CAAC;gBACxC,KAAK,sBAAc,CAAC,kBAAkB;oBACpC,OAAO,IAAI,CAAC;gBAEd,KAAK,sBAAc,CAAC,OAAO,CAAC;gBAC5B,KAAK,sBAAc,CAAC,eAAe;oBACjC,OAAO,KAAK,CAAC;gBAEf,KAAK,sBAAc,CAAC,cAAc;oBAChC,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,cAAc,CAAC,IAAI,CAAC,CAAC;gBAErD;oBACE,OAAO,cAAc,CAAC,IAAI,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;QAED,SAAS,2BAA2B,CAClC,UAAkB,EAClB,IAAmB;YAEnB,MAAM,GAAG,GAAG,OAAO,CAAC,UAAU;iBAC3B,QAAQ,CAAC,IAAI,CAAC;iBACd,UAAU,CAAC,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,KAAK,UAAU,CAAC,CAAC;YAE9D,mCAAmC;YACnC,OAAO,CAAC,GAAG,EAAE,QAAQ,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,CAAC;QAC1D,CAAC;QAED,SAAS,gBAAgB,CACvB,IAAsD;YAEtD,MAAM,UAAU,GAAG,aAAa,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC9C,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACvB,OAAO;YACT,CAAC;YAED,IAAI,UAAU,KAAK,oBAAoB,EAAE,CAAC;gBACxC,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;gBACrD,MAAM,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE,CAAC;gBAChC,IAAI,MAAM,EAAE,CAAC;oBACX,IACE,IAAA,0BAAmB,EAAC,QAAQ,CAAC,OAAO,EAAE,IAAI,EAAE,qBAAqB,CAAC,EAClE,CAAC;wBACD,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,uBAAuB,EAAE,CAAC,CAAC;wBAC7D,OAAO;oBACT,CAAC;gBACH,CAAC;qBAAM,CAAC;oBACN,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,SAAS,EAAE,uBAAuB,EAAE,CAAC,CAAC;oBAC7D,OAAO;gBACT,CAAC;YACH,CAAC;YAED,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBAChC,OAAO;YACT,CAAC;YAED,MAAM,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YACjC,IACE,iBAAiB,CAAC,GAAG,CAAC,UAAU,CAAC;gBACjC,CAAC,UAAU,CAAC,OAAO,CAAC;gBACpB,2BAA2B,CAAC,UAAU,EAAE,IAAI,CAAC,EAC7C,CAAC;gBACD,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,oBAAoB,EAAE,CAAC,CAAC;YACrE,CAAC;QACH,CAAC;QAED,OAAO;YACL,aAAa,EAAE,gBAAgB;YAC/B,cAAc,EAAE,gBAAgB;SACjC,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}