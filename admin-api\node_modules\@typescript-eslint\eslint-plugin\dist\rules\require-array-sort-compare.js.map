{"version": 3, "file": "require-array-sort-compare.js", "sourceRoot": "", "sources": ["../../src/rules/require-array-sort-compare.ts"], "names": [], "mappings": ";;AAEA,kCAMiB;AASjB,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,4BAA4B;IAClC,cAAc,EAAE;QACd;YACE,kBAAkB,EAAE,IAAI;SACzB;KACF;IAED,IAAI,EAAE;QACJ,IAAI,EAAE,SAAS;QACf,IAAI,EAAE;YACJ,WAAW,EACT,uFAAuF;YACzF,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,cAAc,EAAE,6BAA6B;SAC9C;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,kBAAkB,EAAE;wBAClB,WAAW,EACT,6DAA6D;wBAC/D,IAAI,EAAE,SAAS;qBAChB;iBACF;aACF;SACF;KACF;IAED,MAAM,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC;QACvB,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD;;WAEG;QACH,SAAS,iBAAiB,CAAC,IAAyB;YAClD,MAAM,IAAI,GAAG,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC;YAE9C,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC;gBAC3D,MAAM,QAAQ,GAAG,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC;gBAChD,OAAO,QAAQ,CAAC,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,IAAA,kBAAW,EAAC,OAAO,EAAE,GAAG,CAAC,KAAK,QAAQ,CAAC,CAAC;YACvE,CAAC;YACD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,SAAS,iBAAiB,CAAC,MAAiC;YAC1D,MAAM,aAAa,GAAG,IAAA,mCAA4B,EAChD,QAAQ,EACR,MAAM,CAAC,MAAM,CACd,CAAC;YAEF,IAAI,OAAO,CAAC,kBAAkB,IAAI,iBAAiB,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;gBACnE,OAAO;YACT,CAAC;YAED,IAAI,IAAA,yCAAkC,EAAC,aAAa,EAAE,OAAO,CAAC,EAAE,CAAC;gBAC/D,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI,EAAE,MAAM,CAAC,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,CAAC,CAAC;YACvE,CAAC;QACH,CAAC;QAED,OAAO;YACL,6FAA6F,EAC3F,iBAAiB;YACnB,iGAAiG,EAC/F,iBAAiB;SACpB,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}