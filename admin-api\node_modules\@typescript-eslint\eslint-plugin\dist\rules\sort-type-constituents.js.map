{"version": 3, "file": "sort-type-constituents.js", "sourceRoot": "", "sources": ["../../src/rules/sort-type-constituents.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAgF;AAEhF,IAAK,KAaJ;AAbD,WAAK,KAAK;IACR,oCAA2B,CAAA;IAC3B,8BAAqB,CAAA;IACrB,0BAAiB,CAAA;IACjB,sCAA6B,CAAA;IAC7B,4BAAmB,CAAA;IACnB,4BAAmB,CAAA;IACnB,4BAAmB,CAAA;IACnB,wBAAe,CAAA;IACf,0BAAiB,CAAA;IACjB,8BAAqB,CAAA;IACrB,wBAAe,CAAA;IACf,wBAAe,CAAA;AACjB,CAAC,EAbI,KAAK,KAAL,KAAK,QAaT;AAED,SAAS,QAAQ,CAAC,IAAuB;IACvC,QAAQ,IAAI,CAAC,IAAI,EAAE,CAAC;QAClB,KAAK,sBAAc,CAAC,iBAAiB;YACnC,OAAO,KAAK,CAAC,WAAW,CAAC;QAE3B,KAAK,sBAAc,CAAC,iBAAiB,CAAC;QACtC,KAAK,sBAAc,CAAC,cAAc;YAChC,OAAO,KAAK,CAAC,QAAQ,CAAC;QAExB,KAAK,sBAAc,CAAC,YAAY;YAC9B,OAAO,KAAK,CAAC,MAAM,CAAC;QAEtB,KAAK,sBAAc,CAAC,kBAAkB;YACpC,OAAO,KAAK,CAAC,YAAY,CAAC;QAE5B,KAAK,sBAAc,CAAC,YAAY,CAAC;QACjC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,kBAAkB;YACpC,OAAO,KAAK,CAAC,OAAO,CAAC;QAEvB,KAAK,sBAAc,CAAC,aAAa,CAAC;QAClC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,aAAa;YAC/B,OAAO,KAAK,CAAC,OAAO,CAAC;QAEvB,KAAK,sBAAc,CAAC,aAAa,CAAC;QAClC,KAAK,sBAAc,CAAC,qBAAqB;YACvC,OAAO,KAAK,CAAC,OAAO,CAAC;QAEvB,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,mBAAmB,CAAC;QACxC,KAAK,sBAAc,CAAC,WAAW,CAAC;QAChC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe;YACjC,OAAO,KAAK,CAAC,KAAK,CAAC;QAErB,KAAK,sBAAc,CAAC,YAAY,CAAC;QACjC,KAAK,sBAAc,CAAC,aAAa;YAC/B,OAAO,KAAK,CAAC,MAAM,CAAC;QAEtB,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,KAAK,CAAC,QAAQ,CAAC;QAExB,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,KAAK,CAAC,KAAK,CAAC;QAErB,KAAK,sBAAc,CAAC,WAAW;YAC7B,OAAO,KAAK,CAAC,KAAK,CAAC;QAErB,iEAAiE;QACjE,KAAK,sBAAc,CAAC,iBAAiB,CAAC;QACtC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,cAAc,CAAC;QACnC,KAAK,sBAAc,CAAC,gBAAgB,CAAC;QACrC,KAAK,sBAAc,CAAC,kBAAkB,CAAC;QACvC,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,iBAAiB,CAAC;QACtC,KAAK,sBAAc,CAAC,UAAU,CAAC;QAC/B,KAAK,sBAAc,CAAC,eAAe,CAAC;QACpC,KAAK,sBAAc,CAAC,eAAe;YACjC,0BAA0B;YAC1B,MAAM,IAAI,KAAK,CAAC,mBAAmB,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;IACpD,CAAC;AACH,CAAC;AAED,SAAS,iBAAiB,CAAC,CAAS,EAAE,CAAS;IAC7C,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACV,OAAO,CAAC,CAAC,CAAC;IACZ,CAAC;SAAM,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC;QACjB,OAAO,CAAC,CAAC;IACX,CAAC;IACD,OAAO,CAAC,CAAC;AACX,CAAC;AAYD,kBAAe,IAAA,iBAAU,EAAsB;IAC7C,IAAI,EAAE,wBAAwB;IAC9B,IAAI,EAAE;QACJ,UAAU,EAAE,IAAI;QAChB,UAAU,EAAE;YACV,uCAAuC;YACvC,gCAAgC;SACjC;QACD,IAAI,EAAE,YAAY;QAClB,IAAI,EAAE;YACJ,WAAW,EACT,+EAA+E;SAClF;QACD,OAAO,EAAE,MAAM;QACf,cAAc,EAAE,IAAI;QACpB,QAAQ,EAAE;YACR,SAAS,EAAE,4CAA4C;YACvD,cAAc,EAAE,qDAAqD;YACrE,UAAU,EAAE,mDAAmD;SAChE;QACD,MAAM,EAAE;YACN;gBACE,IAAI,EAAE,QAAQ;gBACd,oBAAoB,EAAE,KAAK;gBAC3B,UAAU,EAAE;oBACV,kBAAkB,EAAE;wBAClB,WAAW,EAAE,sCAAsC;wBACnD,IAAI,EAAE,SAAS;qBAChB;oBACD,WAAW,EAAE;wBACX,WAAW,EAAE,+BAA+B;wBAC5C,IAAI,EAAE,SAAS;qBAChB;oBACD,aAAa,EAAE;wBACb,WAAW,EAAE,+CAA+C;wBAC5D,IAAI,EAAE,SAAS;qBAChB;oBACD,UAAU,EAAE;wBACV,WAAW,EAAE,yBAAyB;wBACtC,IAAI,EAAE,OAAO;wBACb,KAAK,EAAE;4BACL,IAAI,EAAE,QAAQ;4BACd,IAAI,EAAE,IAAA,mBAAY,EAAC,KAAK,CAAC;yBAC1B;qBACF;iBACF;aACF;SACF;KACF;IACD,cAAc,EAAE;QACd;YACE,kBAAkB,EAAE,IAAI;YACxB,WAAW,EAAE,IAAI;YACjB,aAAa,EAAE,KAAK;YACpB,UAAU,EAAE;gBACV,KAAK,CAAC,KAAK;gBACX,KAAK,CAAC,OAAO;gBACb,KAAK,CAAC,QAAQ;gBACd,KAAK,CAAC,OAAO;gBACb,KAAK,CAAC,QAAQ;gBACd,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,WAAW;gBACjB,KAAK,CAAC,MAAM;gBACZ,KAAK,CAAC,KAAK;gBACX,KAAK,CAAC,YAAY;gBAClB,KAAK,CAAC,KAAK;gBACX,KAAK,CAAC,OAAO;aACd;SACF;KACF;IACD,MAAM,CACJ,OAAO,EACP,CAAC,EAAE,kBAAkB,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC;QAEhE,MAAM,QAAQ,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACvC,WAAW,EAAE,MAAM;YACnB,OAAO,EAAE,IAAI;SACd,CAAC,CAAC;QAEH,SAAS,YAAY,CACnB,IAAwD;YAExD,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;gBACxC,MAAM,KAAK,GAAG,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;gBACxD,OAAO;oBACL,KAAK,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,KAAK;oBACrD,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC;iBACvC,CAAC;YACJ,CAAC,CAAC,CAAC;YACH,MAAM,aAAa,GAAG,CAAC,GAAG,WAAW,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBACnD,IAAI,CAAC,CAAC,KAAK,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC;oBACxB,OAAO,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC;gBAC3B,CAAC;gBAED,IAAI,aAAa,EAAE,CAAC;oBAClB,OAAO,iBAAiB,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC;gBAC3C,CAAC;gBAED,OAAO,CACL,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC;oBAChC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CACjD,CAAC;YACJ,CAAC,CAAC,CAAC;YAEH,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE;gBACzC,MAAM,KAAK,GACT,OAAO,CAAC,UAAU,CAAC,iBAAiB,CAAC,IAAI,CAAC,CAAC,MAAM;oBACjD,OAAO,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC;gBACnD,OAAO,KAAK,GAAG,CAAC,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjD,IAAI,aAAa,CAAC,CAAC,CAAC,CAAC,IAAI,KAAK,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;oBAClD,IAAI,SAAS,GAAe,WAAW,CAAC;oBACxC,MAAM,IAAI,GAAG;wBACX,IAAI,EAAE,EAAE;wBACR,IAAI,EACF,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;4BAC7C,CAAC,CAAC,cAAc;4BAChB,CAAC,CAAC,OAAO;qBACd,CAAC;oBACF,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,sBAAsB,EAAE,CAAC;wBAC/D,SAAS,GAAG,gBAAgB,CAAC;wBAC7B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC;oBAClC,CAAC;oBAED,MAAM,GAAG,GAA+B,KAAK,CAAC,EAAE;wBAC9C,MAAM,MAAM,GAAG,aAAa;6BACzB,GAAG,CAAC,CAAC,CAAC,EAAE,CACP,IAAA,kCAA2B,EAAC,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,IAAI,CAAC;4BAC3C,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB;gCAC9C,CAAC,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,WAAW,CAAC;4BAC3C,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,GAAG;4BACf,CAAC,CAAC,CAAC,CAAC,IAAI,CACX;6BACA,IAAI,CACH,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,kBAAkB,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,KAAK,CAChE,CAAC;wBAEJ,OAAO,KAAK,CAAC,WAAW,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oBACzC,CAAC,CAAC;oBACF,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI;wBACJ,SAAS;wBACT,IAAI;wBACJ,mEAAmE;wBACnE,kFAAkF;wBAClF,GAAG,CAAC,WAAW;4BACb,CAAC,CAAC;gCACE,OAAO,EAAE;oCACP;wCACE,SAAS,EAAE,YAAY;wCACvB,GAAG;qCACJ;iCACF;6BACF;4BACH,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC;qBACb,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;QACH,CAAC;QAED,OAAO;YACL,GAAG,CAAC,kBAAkB,IAAI;gBACxB,kBAAkB,CAAC,IAAI;oBACrB,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;aACF,CAAC;YACF,GAAG,CAAC,WAAW,IAAI;gBACjB,WAAW,CAAC,IAAI;oBACd,YAAY,CAAC,IAAI,CAAC,CAAC;gBACrB,CAAC;aACF,CAAC;SACH,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}