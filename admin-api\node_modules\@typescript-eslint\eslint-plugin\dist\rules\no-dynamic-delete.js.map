{"version": 3, "file": "no-dynamic-delete.js", "sourceRoot": "", "sources": ["../../src/rules/no-dynamic-delete.ts"], "names": [], "mappings": ";;AACA,oDAA0D;AAE1D,kCAAoE;AAEpE,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,mBAAmB;IACzB,IAAI,EAAE;QACJ,IAAI,EAAE;YACJ,WAAW,EACT,kEAAkE;YACpE,WAAW,EAAE,QAAQ;SACtB;QACD,OAAO,EAAE,MAAM;QACf,QAAQ,EAAE;YACR,aAAa,EAAE,mDAAmD;SACnE;QACD,MAAM,EAAE,EAAE;QACV,IAAI,EAAE,YAAY;KACnB;IACD,cAAc,EAAE,EAAE;IAClB,MAAM,CAAC,OAAO;QACZ,SAAS,WAAW,CAClB,MAAiC;YAEjC,IACE,MAAM,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;gBAC/C,OAAO,MAAM,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ,EACzC,CAAC;gBACD,OAAO,yBAAyB,CAC9B,MAAM,CAAC,QAAQ,EACf,IAAI,MAAM,CAAC,QAAQ,CAAC,KAAK,EAAE,CAC5B,CAAC;YACJ,CAAC;YAED,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,OAAO;YACL,kCAAkC,CAAC,IAA8B;gBAC/D,IACE,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,gBAAgB;oBACtD,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ;oBACvB,2BAA2B,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,EACnD,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,OAAO,CAAC,MAAM,CAAC;oBACb,GAAG,EAAE,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC;oBAC/B,SAAS,EAAE,eAAe;oBAC1B,IAAI,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;iBAC7B,CAAC,CAAC;YACL,CAAC;SACF,CAAC;QAEF,SAAS,yBAAyB,CAChC,QAA6B,EAC7B,WAAmB;YAEnB,OAAO,CAAC,KAAyB,EAAoB,EAAE,CACrD,KAAK,CAAC,gBAAgB,CAAC,aAAa,CAAC,QAAQ,CAAC,EAAE,WAAW,CAAC,CAAC;QACjE,CAAC;QAED,SAAS,aAAa,CAAC,QAA6B;YAClD,OAAO;gBACL,IAAA,iBAAU,EACR,OAAO,CAAC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,EAC3C,wBAAiB,CAAC,YAAY,CAAC,cAAc,EAAE,UAAU,CAAC,CAC3D,CAAC,KAAK,CAAC,CAAC,CAAC;gBACV,IAAA,iBAAU,EACR,OAAO,CAAC,UAAU,CAAC,aAAa,CAAC,QAAQ,CAAC,EAC1C,wBAAiB,CAAC,YAAY,CAAC,aAAa,EAAE,UAAU,CAAC,CAC1D,CAAC,KAAK,CAAC,CAAC,CAAC;aACX,CAAC;QACJ,CAAC;IACH,CAAC;CACF,CAAC,CAAC;AAEH,SAAS,2BAA2B,CAAC,QAA6B;IAChE,OAAO,CACL,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;QACvC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,OAAO,QAAQ,CAAC,KAAK,CAAC,CAAC;QACvD,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,eAAe;YAC/C,QAAQ,CAAC,QAAQ,KAAK,GAAG;YACzB,QAAQ,CAAC,QAAQ,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;YACjD,OAAO,QAAQ,CAAC,QAAQ,CAAC,KAAK,KAAK,QAAQ,CAAC,CAC/C,CAAC;AACJ,CAAC"}